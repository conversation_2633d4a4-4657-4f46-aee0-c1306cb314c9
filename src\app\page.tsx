'use client'

import Image from 'next/image'
import Link from 'next/link'
import { useState } from 'react'
import { ThemeController } from '@/components/ThemeController'

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }
  return (
    <div className="grid grid-cols-12 overflow-auto h-screen">
      <div className="col-span-12 md:col-span-6 lg:col-span-5 xl:col-span-4 flex items-center justify-center min-h-screen p-8 sm:p-10 lg:p-12">
        <div className="w-full max-w-md space-y-10">
          <div className="flex items-center justify-between relative">
            <Link href="/">
              <Image src="/logo-light.svg" alt="logo" width={103} height={20} />
            </Link>
            <ThemeController />
          </div>
          <div className="text-center space-y-2">
            <h3 className="text-3xl font-semibold">Welcome back!</h3>
            <p className="text-sm text-base-content/70">
              Enter your credentials to log in to the application
            </p>
          </div>
          <div className="space-y-4">
            <fieldset className="fieldset mb-0">
              <legend className="fieldset-legend text-sm">Email Address</legend>
              <label className="input w-full focus:outline-0">
                <span className="icon-[solar--mailbox-bold-duotone]"></span>
                <input type="email" placeholder="Email Address" className="grow focus:outline-0" />
              </label>
            </fieldset>
            <fieldset className="fieldset mb-0">
              <legend className="fieldset-legend text-sm">Password</legend>
              <label className="input w-full focus:outline-0">
                <span className="icon-[solar--key-bold-duotone]"></span>
                <input
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Password"
                  className="grow focus:outline-0"
                />
                <button
                  type="button"
                  className="btn btn-xs btn-ghost btn-circle"
                  onClick={togglePasswordVisibility}
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                >
                  <span
                    className={
                      showPassword
                        ? 'icon-[solar--eye-closed-bold-duotone]'
                        : 'icon-[solar--eye-bold-duotone]'
                    }
                    aria-hidden="true"
                  ></span>
                </button>
              </label>
            </fieldset>
            <div className="text-end">
              <Link href="/auth/forgot-password" className="text-xs">
                Forgot Password?
              </Link>
            </div>
            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="agreement"
                className="checkbox checkbox-sm checkbox-primary"
                aria-label="Terms agreement"
              />
              <label htmlFor="agreement" className="text-sm">
                I agree with
                <span className="text-primary cursor-pointer ms-1 hover:underline">
                  terms and conditions
                </span>
              </label>
            </div>
            <div className="flex flex-col gap-3 w-full">
              <button className="btn btn-primary w-full gap-3">
                <span className="icon-[solar--login-2-bold-duotone]"></span>
                Login
              </button>
              <button className="btn btn-ghost w-full border border-base-300 gap-3">
                <Image src="/google-mini.svg" alt="Google logo" width={20} height={20}></Image>
                Login with Google
              </button>
            </div>
            <p className="text-center text-xs text-base-content/80">
              No account yet?
              <Link href="/auth/sign-up" className="text-primary ms-1 hover:underline">
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>
      <div className="relative hidden col-span-12 md:col-span-6 lg:col-span-7 xl:col-span-8 md:block">
        <Image src="/placeholder.svg" alt="placeholder" fill priority />
      </div>
    </div>
  )
}
